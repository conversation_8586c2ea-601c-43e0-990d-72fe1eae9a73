<div class="api-test-container">
  <div class="header">
    <h1>🔗 Frontend-Backend Connection Test</h1>
    <p>Testing connectivity between Angular frontend and microservices backend</p>
    
    <div class="environment-info">
      <h3>Environment Configuration:</h3>
      <ul>
        <li><strong>API Gateway:</strong> {{ environment.apiGatewayUrl }}</li>
        <li><strong>Users API:</strong> {{ environment.apiUrls.users }}</li>
        <li><strong>Formations API:</strong> {{ environment.apiUrls.formations }}</li>
        <li><strong>Formulaires API:</strong> {{ environment.apiUrls.formulaires }}</li>
        <li><strong>Responses API:</strong> {{ environment.apiUrls.responses }}</li>
      </ul>
    </div>
  </div>

  <div class="test-results">
    <h2>🧪 Test Results</h2>
    
    <div class="test-grid">
      <!-- API Gateway Test -->
      <div class="test-card" [ngClass]="getStatusClass(testResults.apiGateway.status)">
        <div class="test-header">
          <h3>{{ getStatusIcon(testResults.apiGateway.status) }} API Gateway</h3>
          <button class="retry-btn" (click)="retryTest('apiGateway')" [disabled]="loading">
            🔄 Retry
          </button>
        </div>
        <div class="test-content">
          <p class="status-message">{{ testResults.apiGateway.message }}</p>
          <div *ngIf="testResults.apiGateway.data" class="test-data">
            <pre>{{ testResults.apiGateway.data | json }}</pre>
          </div>
        </div>
      </div>

      <!-- User Service Test -->
      <div class="test-card" [ngClass]="getStatusClass(testResults.userService.status)">
        <div class="test-header">
          <h3>{{ getStatusIcon(testResults.userService.status) }} User Service</h3>
          <button class="retry-btn" (click)="retryTest('userService')" [disabled]="loading">
            🔄 Retry
          </button>
        </div>
        <div class="test-content">
          <p class="status-message">{{ testResults.userService.message }}</p>
          <div *ngIf="testResults.userService.data" class="test-data">
            <h4>Sample Users:</h4>
            <div *ngFor="let user of testResults.userService.data" class="data-item">
              <strong>{{ user.firstName }} {{ user.lastName }}</strong> ({{ user.role }})
            </div>
          </div>
        </div>
      </div>

      <!-- Formation Service Test -->
      <div class="test-card" [ngClass]="getStatusClass(testResults.formationService.status)">
        <div class="test-header">
          <h3>{{ getStatusIcon(testResults.formationService.status) }} Formation Service</h3>
          <button class="retry-btn" (click)="retryTest('formationService')" [disabled]="loading">
            🔄 Retry
          </button>
        </div>
        <div class="test-content">
          <p class="status-message">{{ testResults.formationService.message }}</p>
          <div *ngIf="testResults.formationService.data" class="test-data">
            <h4>Sample Formations:</h4>
            <div *ngFor="let formation of testResults.formationService.data" class="data-item">
              <strong>{{ formation.nom }}</strong> - {{ formation.description }}
            </div>
          </div>
        </div>
      </div>

      <!-- Formulaire Service Test -->
      <div class="test-card" [ngClass]="getStatusClass(testResults.formulaireService.status)">
        <div class="test-header">
          <h3>{{ getStatusIcon(testResults.formulaireService.status) }} Formulaire Service</h3>
          <button class="retry-btn" (click)="retryTest('formulaireService')" [disabled]="loading">
            🔄 Retry
          </button>
        </div>
        <div class="test-content">
          <p class="status-message">{{ testResults.formulaireService.message }}</p>
          <div *ngIf="testResults.formulaireService.data" class="test-data">
            <h4>Sample Formulaires:</h4>
            <div *ngFor="let formulaire of testResults.formulaireService.data" class="data-item">
              <strong>{{ formulaire.titre }}</strong> - {{ formulaire.type }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="actions">
      <button class="test-all-btn" (click)="runAllTests()" [disabled]="loading">
        <span *ngIf="loading">🔄 Testing...</span>
        <span *ngIf="!loading">🚀 Run All Tests</span>
      </button>
    </div>
  </div>

  <div class="instructions">
    <h3>📋 Setup Instructions</h3>
    <ol>
      <li>Make sure all backend services are running:
        <ul>
          <li>API Gateway on port 7000</li>
          <li>User Service on port 5206</li>
          <li>Formation Service on port 5080</li>
          <li>Formulaire Service on port 7179</li>
          <li>Score Service on port 8080</li>
        </ul>
      </li>
      <li>Ensure databases are connected and migrated</li>
      <li>Check CORS configuration is properly set up</li>
      <li>Verify API Gateway routes in ocelot.json</li>
    </ol>
  </div>
</div>
