import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { BaseApiService } from './base-api.service';
import {
  User,
  LoginRequest,
  LoginResponse,
  AddUserDto,
  ChangePasswordDto
} from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class UserService extends BaseApiService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(http: HttpClient) {
    super(http);
    this.loadUserFromStorage();
  }

  private loadUserFromStorage(): void {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing user from localStorage:', error);
        localStorage.removeItem('user');
      }
    }
  }

  login(credentials: LoginRequest): Observable<any> {
    return this.post<any>('/users/login', credentials)
      .pipe(
        tap(response => {
          // Handle first login scenario
          if (response.firstLogin) {
            // Store user info for password change
            localStorage.setItem('tempUserId', response.userId.toString());
            localStorage.setItem('tempUserRole', response.role);
            return;
          }

          // Handle successful login
          if (response.token) {
            localStorage.setItem('token', response.token);

            // Create user object from response
            const user: User = {
              id: response.user?.id || 0,
              firstName: response.user?.firstName || '',
              lastName: response.user?.lastName || '',
              email: response.user?.email || '',
              role: response.user?.role || '',
              formationId: response.user?.formationId || 0,
              isFirstLogin: false
            };

            localStorage.setItem('user', JSON.stringify(user));
            this.currentUserSubject.next(user);
          }
        })
      );
  }

  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    this.currentUserSubject.next(null);
  }

  isLoggedIn(): boolean {
    return !!localStorage.getItem('token');
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  addStudent(studentData: AddUserDto): Observable<any> {
    return this.post('/users/add-student', studentData);
  }

  addProfessor(professorData: AddUserDto): Observable<any> {
    return this.post('/users/add-prof', professorData);
  }

  addAdmin(adminData: AddUserDto): Observable<any> {
    return this.post('/users/add-admin', adminData);
  }

  changePassword(passwordData: ChangePasswordDto): Observable<any> {
    return this.post('/users/change-password', passwordData);
  }

  getAllUsers(): Observable<User[]> {
    return this.get<User[]>('/users');
  }

  getUserById(id: number): Observable<User> {
    return this.get<User>(`/users/${id}`);
  }

  updateUser(id: number, userData: Partial<User>): Observable<User> {
    return this.put<User>(`/users/${id}`, userData);
  }

  deleteUser(id: number): Observable<any> {
    return this.delete(`/users/${id}`);
  }
}
