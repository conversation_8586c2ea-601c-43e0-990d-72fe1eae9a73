<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h2>تسجيل الدخول</h2>
      <p>منصة تقييم البرامج الأكاديمية</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <div class="form-group">
        <label for="email">البريد الإلكتروني</label>
        <input 
          type="email" 
          id="email"
          formControlName="email"
          class="form-control"
          [class.is-invalid]="f['email'].invalid && f['email'].touched"
          placeholder="أدخل بريدك الإلكتروني"
        >
        <div *ngIf="f['email'].invalid && f['email'].touched" class="invalid-feedback">
          <div *ngIf="f['email'].errors?.['required']">البريد الإلكتروني مطلوب</div>
          <div *ngIf="f['email'].errors?.['email']">يرجى إدخال بريد إلكتروني صحيح</div>
        </div>
      </div>

      <div class="form-group">
        <label for="password">كلمة المرور</label>
        <input 
          type="password" 
          id="password"
          formControlName="password"
          class="form-control"
          [class.is-invalid]="f['password'].invalid && f['password'].touched"
          placeholder="أدخل كلمة المرور"
        >
        <div *ngIf="f['password'].invalid && f['password'].touched" class="invalid-feedback">
          <div *ngIf="f['password'].errors?.['required']">كلمة المرور مطلوبة</div>
        </div>
      </div>

      <div *ngIf="error" class="alert alert-danger">
        {{ error }}
      </div>

      <button 
        type="submit" 
        class="btn btn-primary btn-block"
        [disabled]="loading || loginForm.invalid"
      >
        <span *ngIf="loading" class="spinner-border spinner-border-sm" role="status"></span>
        {{ loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول' }}
      </button>
    </form>

    <div class="login-footer">
      <p>نسيت كلمة المرور؟ <a href="#" class="text-primary">اضغط هنا</a></p>
    </div>
  </div>
</div>
