import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class BaseApiService {
  protected apiUrl = environment.apiGatewayUrl;

  constructor(protected http: HttpClient) {}

  protected getHttpOptions(): { headers: HttpHeaders } {
    const token = localStorage.getItem('token');
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      })
    };
  }

  protected handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'حدث خطأ غير معروف';
    
    if (error.error instanceof ErrorEvent) {
      // خطأ من جانب العميل
      errorMessage = `خطأ: ${error.error.message}`;
    } else {
      // خطأ من جانب الخادم
      errorMessage = `كود الخطأ: ${error.status}\nالرسالة: ${error.message}`;
      
      if (error.status === 401) {
        errorMessage = 'غير مصرح لك بالوصول';
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      } else if (error.status === 404) {
        errorMessage = 'المورد المطلوب غير موجود';
      } else if (error.status === 500) {
        errorMessage = 'خطأ في الخادم';
      }
    }

    console.error('API Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  protected get<T>(endpoint: string): Observable<T> {
    return this.http.get<T>(`${this.apiUrl}${endpoint}`, this.getHttpOptions())
      .pipe(catchError(this.handleError.bind(this)));
  }

  protected post<T>(endpoint: string, data: any): Observable<T> {
    return this.http.post<T>(`${this.apiUrl}${endpoint}`, data, this.getHttpOptions())
      .pipe(catchError(this.handleError.bind(this)));
  }

  protected put<T>(endpoint: string, data: any): Observable<T> {
    return this.http.put<T>(`${this.apiUrl}${endpoint}`, data, this.getHttpOptions())
      .pipe(catchError(this.handleError.bind(this)));
  }

  protected delete<T>(endpoint: string): Observable<T> {
    return this.http.delete<T>(`${this.apiUrl}${endpoint}`, this.getHttpOptions())
      .pipe(catchError(this.handleError.bind(this)));
  }
}
