export interface Formation {
  formationId: number;
  formationName: string;
  schoolName: string;
  description: string;
  modules: Module[];
}

export interface Module {
  moduleId: number;
  moduleName: string;
  niveaux: Niveau[];
}

export interface Niveau {
  niveauId: number;
  niveauName: string;
}

export interface FormationCreateDTO {
  formationName: string;
  schoolName: string;
  description: string;
  modules: ModuleDTO[];
}

export interface ModuleDTO {
  moduleName: string;
  niveaux: NiveauDTO[];
}

export interface NiveauDTO {
  niveauName: string;
}

export interface FormationUpdateDTO {
  formationName: string;
  schoolName: string;
  description: string;
}

export interface FormationResponseDTO {
  formationId: number;
  formationName: string;
  schoolName: string;
  description: string;
  modules: ModuleResponse[];
}

export interface ModuleResponse {
  moduleId: number;
  moduleName: string;
  niveaux: NiveauResponse[];
}

export interface NiveauResponse {
  niveauId: number;
  niveauName: string;
}
