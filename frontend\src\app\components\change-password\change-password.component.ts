import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.css']
})
export class ChangePasswordComponent implements OnInit {
  changePasswordForm: FormGroup;
  loading = false;
  error = '';
  success = '';
  userId: number = 0;
  userRole: string = '';

  constructor(
    private formBuilder: FormBuilder,
    private userService: UserService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.changePasswordForm = this.formBuilder.group({
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    // Get user info from query params or localStorage
    this.userId = Number(this.route.snapshot.queryParams['userId']) || 
                  Number(localStorage.getItem('tempUserId'));
    this.userRole = this.route.snapshot.queryParams['role'] || 
                    localStorage.getItem('tempUserRole') || '';

    if (!this.userId) {
      this.router.navigate(['/login']);
    }
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  get f() { return this.changePasswordForm.controls; }

  onSubmit(): void {
    if (this.changePasswordForm.invalid) {
      return;
    }

    this.loading = true;
    this.error = '';
    this.success = '';

    const passwordData = {
      userId: this.userId,
      newPassword: this.f['newPassword'].value
    };

    this.userService.changePassword(passwordData).subscribe({
      next: (response) => {
        this.loading = false;
        this.success = 'تم تغيير كلمة المرور بنجاح';
        
        // Clear temporary data
        localStorage.removeItem('tempUserId');
        localStorage.removeItem('tempUserRole');
        
        // Redirect to login after 2 seconds
        setTimeout(() => {
          this.router.navigate(['/login']);
        }, 2000);
      },
      error: (error) => {
        this.loading = false;
        this.error = error.message || 'حدث خطأ أثناء تغيير كلمة المرور';
      }
    });
  }
}
