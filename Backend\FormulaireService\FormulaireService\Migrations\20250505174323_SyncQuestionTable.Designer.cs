﻿// <auto-generated />
using FormulaireService;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FormulaireService.Migrations
{
    [DbContext(typeof(FormulaireDbContext))]
    [Migration("20250505174323_SyncQuestionTable")]
    partial class SyncQuestionTable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FormulaireService.Models.Formulaire", b =>
                {
                    b.Property<int>("FormulaireId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("FormulaireId"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("FormulaireId");

                    b.ToTable("Formulaires");
                });

            modelBuilder.Entity("FormulaireService.Models.Question", b =>
                {
                    b.Property<int>("QuestionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("QuestionId"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("SectionFormId")
                        .HasColumnType("integer");

                    b.Property<int>("SectionFormulaireSecFormId")
                        .HasColumnType("integer");

                    b.HasKey("QuestionId");

                    b.HasIndex("SectionFormulaireSecFormId");

                    b.ToTable("Questions");
                });

            modelBuilder.Entity("FormulaireService.Models.SectionFormulaire", b =>
                {
                    b.Property<int>("SecFormId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("SecFormId"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("FormulaireId")
                        .HasColumnType("integer");

                    b.HasKey("SecFormId");

                    b.HasIndex("FormulaireId");

                    b.ToTable("SectionFormulaires");
                });

            modelBuilder.Entity("FormulaireService.Models.Question", b =>
                {
                    b.HasOne("FormulaireService.Models.SectionFormulaire", "SectionFormulaire")
                        .WithMany("Questions")
                        .HasForeignKey("SectionFormulaireSecFormId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SectionFormulaire");
                });

            modelBuilder.Entity("FormulaireService.Models.SectionFormulaire", b =>
                {
                    b.HasOne("FormulaireService.Models.Formulaire", "Formulaire")
                        .WithMany("Sections")
                        .HasForeignKey("FormulaireId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Formulaire");
                });

            modelBuilder.Entity("FormulaireService.Models.Formulaire", b =>
                {
                    b.Navigation("Sections");
                });

            modelBuilder.Entity("FormulaireService.Models.SectionFormulaire", b =>
                {
                    b.Navigation("Questions");
                });
#pragma warning restore 612, 618
        }
    }
}
