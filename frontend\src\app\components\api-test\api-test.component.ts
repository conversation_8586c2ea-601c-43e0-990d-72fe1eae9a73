import { Component, OnInit } from '@angular/core';
import { UserService } from '../../services/user.service';
import { FormationService } from '../../services/formation.service';
import { FormulaireService } from '../../services/formulaire.service';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-api-test',
  templateUrl: './api-test.component.html',
  styleUrls: ['./api-test.component.css']
})
export class ApiTestComponent implements OnInit {
  testResults: any = {
    userService: { status: 'pending', message: '', data: null },
    formationService: { status: 'pending', message: '', data: null },
    formulaireService: { status: 'pending', message: '', data: null },
    apiGateway: { status: 'pending', message: '', data: null }
  };

  environment = environment;
  loading = false;

  constructor(
    private userService: UserService,
    private formationService: FormationService,
    private formulaireService: FormulaireService
  ) {}

  ngOnInit(): void {
    this.runAllTests();
  }

  async runAllTests(): Promise<void> {
    this.loading = true;
    
    // Test API Gateway connectivity
    await this.testApiGateway();
    
    // Test User Service
    await this.testUserService();
    
    // Test Formation Service
    await this.testFormationService();
    
    // Test Formulaire Service
    await this.testFormulaireService();
    
    this.loading = false;
  }

  async testApiGateway(): Promise<void> {
    try {
      const response = await fetch(`${environment.apiGatewayUrl}/users`);
      if (response.ok || response.status === 401) {
        this.testResults.apiGateway = {
          status: 'success',
          message: 'API Gateway is accessible',
          data: { status: response.status, statusText: response.statusText }
        };
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      this.testResults.apiGateway = {
        status: 'error',
        message: `API Gateway connection failed: ${error.message}`,
        data: null
      };
    }
  }

  async testUserService(): Promise<void> {
    try {
      this.userService.getAllUsers().subscribe({
        next: (users) => {
          this.testResults.userService = {
            status: 'success',
            message: `User Service working - Found ${users.length} users`,
            data: users.slice(0, 3) // Show first 3 users
          };
        },
        error: (error) => {
          this.testResults.userService = {
            status: 'error',
            message: `User Service error: ${error.message}`,
            data: null
          };
        }
      });
    } catch (error: any) {
      this.testResults.userService = {
        status: 'error',
        message: `User Service connection failed: ${error.message}`,
        data: null
      };
    }
  }

  async testFormationService(): Promise<void> {
    try {
      this.formationService.getAllFormations().subscribe({
        next: (formations) => {
          this.testResults.formationService = {
            status: 'success',
            message: `Formation Service working - Found ${formations.length} formations`,
            data: formations.slice(0, 3) // Show first 3 formations
          };
        },
        error: (error) => {
          this.testResults.formationService = {
            status: 'error',
            message: `Formation Service error: ${error.message}`,
            data: null
          };
        }
      });
    } catch (error: any) {
      this.testResults.formationService = {
        status: 'error',
        message: `Formation Service connection failed: ${error.message}`,
        data: null
      };
    }
  }

  async testFormulaireService(): Promise<void> {
    try {
      this.formulaireService.getAllFormulaires().subscribe({
        next: (formulaires) => {
          this.testResults.formulaireService = {
            status: 'success',
            message: `Formulaire Service working - Found ${formulaires.length} formulaires`,
            data: formulaires.slice(0, 3) // Show first 3 formulaires
          };
        },
        error: (error) => {
          this.testResults.formulaireService = {
            status: 'error',
            message: `Formulaire Service error: ${error.message}`,
            data: null
          };
        }
      });
    } catch (error: any) {
      this.testResults.formulaireService = {
        status: 'error',
        message: `Formulaire Service connection failed: ${error.message}`,
        data: null
      };
    }
  }

  retryTest(serviceName: string): void {
    this.testResults[serviceName] = { status: 'pending', message: '', data: null };
    
    switch (serviceName) {
      case 'apiGateway':
        this.testApiGateway();
        break;
      case 'userService':
        this.testUserService();
        break;
      case 'formationService':
        this.testFormationService();
        break;
      case 'formulaireService':
        this.testFormulaireService();
        break;
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'success': return 'status-success';
      case 'error': return 'status-error';
      case 'pending': return 'status-pending';
      default: return '';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      default: return '❓';
    }
  }
}
