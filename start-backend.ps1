# PowerShell script to start all backend services
# Run this script from the project root directory

Write-Host "🚀 Starting RateMyCourse Backend Services..." -ForegroundColor Green

# Function to start a service in a new terminal
function Start-Service {
    param(
        [string]$ServiceName,
        [string]$Path,
        [string]$Command,
        [int]$Port
    )
    
    Write-Host "Starting $ServiceName on port $Port..." -ForegroundColor Yellow
    
    # Start the service in a new PowerShell window
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$Path'; Write-Host 'Starting $ServiceName...' -ForegroundColor Green; $Command"
    
    Start-Sleep -Seconds 2
}

# Check if we're in the correct directory
if (-not (Test-Path "Backend")) {
    Write-Host "❌ Error: Please run this script from the project root directory (where Backend folder exists)" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Starting services in order..." -ForegroundColor Cyan

# 1. Start API Gateway (Port 7000)
Start-Service -ServiceName "API Gateway" -Path "Backend\ApiGateway" -Command "dotnet run" -Port 7000

# 2. Start User Service (Port 5206)
Start-Service -ServiceName "User Service" -Path "Backend\UserService\UserService" -Command "dotnet run" -Port 5206

# 3. Start Formation Service (Port 5080)
Start-Service -ServiceName "Formation Service" -Path "Backend\FormationService\FormationService" -Command "dotnet run" -Port 5080

# 4. Start Formulaire Service (Port 7179)
Start-Service -ServiceName "Formulaire Service" -Path "Backend\FormulaireService\FormulaireService" -Command "dotnet run" -Port 7179

# 5. Start Score Service (Port 8080)
Start-Service -ServiceName "Score Service" -Path "Backend\ScoreService" -Command "mvn spring-boot:run" -Port 8080

Write-Host "✅ All backend services are starting..." -ForegroundColor Green
Write-Host ""
Write-Host "📊 Service Status:" -ForegroundColor Cyan
Write-Host "  • API Gateway:      http://localhost:7000" -ForegroundColor White
Write-Host "  • User Service:     http://localhost:5206" -ForegroundColor White
Write-Host "  • Formation Service: http://localhost:5080" -ForegroundColor White
Write-Host "  • Formulaire Service: http://localhost:7179" -ForegroundColor White
Write-Host "  • Score Service:    http://localhost:8080" -ForegroundColor White
Write-Host ""
Write-Host "⏳ Please wait for all services to fully start before testing..." -ForegroundColor Yellow
Write-Host "🌐 You can now start the frontend with: cd frontend && npm start" -ForegroundColor Green
Write-Host "🧪 Test the connection at: http://localhost:4200/api-test" -ForegroundColor Green

# Keep the main window open
Write-Host ""
Write-Host "Press any key to close this window..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
