import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { UserService } from './user.service';
import { User, LoginRequest } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    private userService: UserService,
    private router: Router
  ) {
    this.checkAuthStatus();
  }

  private checkAuthStatus(): void {
    const token = localStorage.getItem('token');
    this.isAuthenticatedSubject.next(!!token);
  }

  login(credentials: LoginRequest): Observable<any> {
    return new Observable(observer => {
      this.userService.login(credentials).subscribe({
        next: (response) => {
          // Handle first login scenario
          if (response.firstLogin) {
            observer.next({
              success: false,
              firstLogin: true,
              message: response.message,
              userId: response.userId,
              role: response.role
            });
            return;
          }

          // Handle successful login
          if (response.token) {
            this.isAuthenticatedSubject.next(true);
            observer.next({
              success: true,
              message: response.message || 'تم تسجيل الدخول بنجاح',
              user: response.user
            });
          } else {
            observer.error(response.message || 'فشل في تسجيل الدخول');
          }
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  logout(): void {
    this.userService.logout();
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(['/login']);
  }

  isLoggedIn(): boolean {
    return this.userService.isLoggedIn();
  }

  getCurrentUser(): User | null {
    return this.userService.getCurrentUser();
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user ? user.role === role : false;
  }

  isAdmin(): boolean {
    return this.hasRole('Admin');
  }

  isStudent(): boolean {
    return this.hasRole('Etudiant');
  }

  isProfessor(): boolean {
    return this.hasRole('Prof');
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }
}
