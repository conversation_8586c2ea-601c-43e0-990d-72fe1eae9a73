﻿// <auto-generated />
using FormationService;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FormationService.Migrations
{
    [DbContext(typeof(FormationDbContext))]
    [Migration("20250504103954_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FormationService.Models.Formation", b =>
                {
                    b.Property<int>("FormationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("FormationId"));

                    b.Property<string>("FormationName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SchoolName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("FormationId");

                    b.ToTable("Formations");
                });

            modelBuilder.Entity("FormationService.Models.Module", b =>
                {
                    b.Property<int>("ModuleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("ModuleId"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("ModuleId");

                    b.ToTable("Modules");
                });

            modelBuilder.Entity("FormationService.Models.ModuleFormation", b =>
                {
                    b.Property<int>("FormationId")
                        .HasColumnType("integer");

                    b.Property<int>("ModuleId")
                        .HasColumnType("integer");

                    b.Property<int>("NiveauId")
                        .HasColumnType("integer");

                    b.HasKey("FormationId", "ModuleId", "NiveauId");

                    b.HasIndex("ModuleId");

                    b.HasIndex("NiveauId");

                    b.ToTable("ModuleFormations");
                });

            modelBuilder.Entity("FormationService.Models.Niveau", b =>
                {
                    b.Property<int>("NiveauId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("NiveauId"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("NiveauId");

                    b.ToTable("Niveaux");
                });

            modelBuilder.Entity("FormationService.Models.ModuleFormation", b =>
                {
                    b.HasOne("FormationService.Models.Formation", "Formation")
                        .WithMany("ModuleFormations")
                        .HasForeignKey("FormationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FormationService.Models.Module", "Module")
                        .WithMany("ModuleFormations")
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FormationService.Models.Niveau", "Niveau")
                        .WithMany("ModuleFormations")
                        .HasForeignKey("NiveauId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Formation");

                    b.Navigation("Module");

                    b.Navigation("Niveau");
                });

            modelBuilder.Entity("FormationService.Models.Formation", b =>
                {
                    b.Navigation("ModuleFormations");
                });

            modelBuilder.Entity("FormationService.Models.Module", b =>
                {
                    b.Navigation("ModuleFormations");
                });

            modelBuilder.Entity("FormationService.Models.Niveau", b =>
                {
                    b.Navigation("ModuleFormations");
                });
#pragma warning restore 612, 618
        }
    }
}
