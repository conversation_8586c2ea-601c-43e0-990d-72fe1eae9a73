{"Routes": [{"DownstreamPathTemplate": "/api/users/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5206}], "UpstreamPathTemplate": "/api-gateway/users/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/formationservice/formations/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5080}], "UpstreamPathTemplate": "/api-gateway/formations/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/Formulaires/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 7179}], "UpstreamPathTemplate": "/api-gateway/Formulaires/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/responses/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 8080}], "UpstreamPathTemplate": "/api-gateway/responses/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}], "GlobalConfiguration": {"BaseUrl": "http://localhost:7000"}}