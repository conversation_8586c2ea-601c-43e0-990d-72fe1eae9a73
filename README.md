# 🎓 RateMyCourse - Course Evaluation System

A comprehensive microservices-based course evaluation system built with Angular frontend and .NET Core/.NET 6/Spring Boot backend services.

## 🏗️ Architecture

### Frontend
- **Angular 19** - Modern web application framework
- **TypeScript** - Type-safe JavaScript
- **Bootstrap/Custom CSS** - Responsive UI design

### Backend Microservices
- **API Gateway** (Port 7000) - Ocelot-based routing and load balancing
- **User Service** (Port 5206) - .NET Core - User management and authentication
- **Formation Service** (Port 5080) - .NET 6 - Course and module management
- **Formulaire Service** (Port 7179) - .NET Core - Survey and form management
- **Score Service** (Port 8080) - Spring Boot - Response processing and analytics

### Database
- **PostgreSQL** - Primary database for all services

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v18 or higher)
- **.NET 6 SDK**
- **Java 11+** and **Maven**
- **PostgreSQL**
- **Git**

### 1. <PERSON>lone the Repository
```bash
git clone <repository-url>
cd RateMyCourse-master
```

### 2. Database Setup
1. Install PostgreSQL
2. Create databases for each service:
   ```sql
   CREATE DATABASE userservice_db;
   CREATE DATABASE formationservice_db;
   CREATE DATABASE formulaireservice_db;
   CREATE DATABASE scoreservice_db;
   ```
3. Update connection strings in each service's `appsettings.json`

### 3. Start Backend Services

#### Option A: Using PowerShell Script (Recommended)
```powershell
# Run from project root
.\start-backend.ps1
```

#### Option B: Manual Start
Open separate terminals for each service:

```bash
# Terminal 1 - API Gateway
cd Backend/ApiGateway
dotnet run

# Terminal 2 - User Service
cd Backend/UserService/UserService
dotnet run

# Terminal 3 - Formation Service
cd Backend/FormationService/FormationService
dotnet run

# Terminal 4 - Formulaire Service
cd Backend/FormulaireService/FormulaireService
dotnet run

# Terminal 5 - Score Service
cd Backend/ScoreService
mvn spring-boot:run
```

### 4. Start Frontend

#### Option A: Using PowerShell Script
```powershell
# Run from project root
.\start-frontend.ps1
```

#### Option B: Manual Start
```bash
cd frontend
npm install
npm start
```

## 🧪 Testing the Connection

1. Start all backend services
2. Start the frontend
3. Navigate to: `http://localhost:4200/api-test`
4. Run the connection tests to verify all services are working

## 📊 Service Endpoints

| Service | Port | Health Check | Swagger |
|---------|------|-------------|---------|
| API Gateway | 7000 | `http://localhost:7000` | N/A |
| User Service | 5206 | `http://localhost:5206/api/users` | `http://localhost:5206/swagger` |
| Formation Service | 5080 | `http://localhost:5080/api/formationservice/formations` | `http://localhost:5080/swagger` |
| Formulaire Service | 7179 | `https://localhost:7179/api/Formulaires` | `https://localhost:7179/swagger` |
| Score Service | 8080 | `http://localhost:8080/api/responses` | `http://localhost:8080/swagger-ui.html` |

## 🔧 Configuration

### Frontend Environment
File: `frontend/src/environments/environment.ts`
```typescript
export const environment = {
  production: false,
  apiGatewayUrl: 'http://localhost:7000/api-gateway',
  apiUrls: {
    users: 'http://localhost:7000/api-gateway/users',
    formations: 'http://localhost:7000/api-gateway/formations',
    formulaires: 'http://localhost:7000/api-gateway/Formulaires',
    responses: 'http://localhost:7000/api-gateway/responses'
  }
};
```

### API Gateway Routes
File: `Backend/ApiGateway/ocelot.json`
- Routes all frontend requests to appropriate microservices
- Handles load balancing and service discovery

## 🔐 Authentication Flow

1. **Login**: User submits credentials to User Service via API Gateway
2. **First Login**: System prompts for password change
3. **JWT Token**: Generated for authenticated sessions
4. **Role-based Access**: Admin, Professor, Student roles

## 🛠️ Development

### Adding New Features
1. **Backend**: Add controllers/services to appropriate microservice
2. **Frontend**: Create components and services in Angular
3. **API Gateway**: Update routes in `ocelot.json` if needed
4. **Database**: Run migrations for schema changes

### Code Structure
```
RateMyCourse-master/
├── Backend/
│   ├── ApiGateway/          # Ocelot API Gateway
│   ├── UserService/         # User management (.NET Core)
│   ├── FormationService/    # Course management (.NET 6)
│   ├── FormulaireService/   # Survey management (.NET Core)
│   └── ScoreService/        # Analytics (Spring Boot)
├── frontend/                # Angular application
│   ├── src/app/
│   │   ├── components/      # UI components
│   │   ├── services/        # API services
│   │   ├── models/          # TypeScript interfaces
│   │   └── guards/          # Route guards
│   └── src/environments/    # Environment configs
├── start-backend.ps1        # Backend startup script
├── start-frontend.ps1       # Frontend startup script
└── README.md
```

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure all services have CORS configured for `http://localhost:4200`
   - Check browser console for specific CORS messages

2. **Database Connection**
   - Verify PostgreSQL is running
   - Check connection strings in `appsettings.json`
   - Run database migrations

3. **Port Conflicts**
   - Ensure no other applications are using the required ports
   - Check Windows Firewall settings

4. **Service Not Starting**
   - Check console output for specific error messages
   - Verify all dependencies are installed
   - Check database connectivity

### API Test Results
Use the built-in API test page at `http://localhost:4200/api-test` to:
- Verify all services are running
- Test API Gateway connectivity
- Check service health status
- View sample data from each service

## 📝 Features

- ✅ **User Management**: Registration, authentication, role-based access
- ✅ **Course Management**: Create and manage formations and modules
- ✅ **Survey System**: Dynamic form creation and management
- ✅ **Response Processing**: Collect and analyze student feedback
- ✅ **Analytics Dashboard**: View statistics and reports
- ✅ **Microservices Architecture**: Scalable and maintainable design
- ✅ **API Gateway**: Centralized routing and load balancing
- ✅ **CORS Configuration**: Proper frontend-backend communication
- ✅ **Connection Testing**: Built-in API connectivity verification

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly using the API test page
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details. : Academic Program Evaluation Platform

A centralized web application that automates academic program evaluations by replacing manual methods with a unified, secure, and user-friendly platform for collecting and analyzing feedback.
