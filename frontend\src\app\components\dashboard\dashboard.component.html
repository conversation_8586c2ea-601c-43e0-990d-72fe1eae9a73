<div class="dashboard-container">
  <!-- Header -->
  <header class="dashboard-header">
    <div class="header-content">
      <div class="user-info">
        <h1>مرحباً، {{ currentUser?.firstName }} {{ currentUser?.lastName }}</h1>
        <p class="user-role">{{ getUserRoleDisplay() }}</p>
      </div>
      <button class="btn btn-outline-danger" (click)="logout()">
        تسجيل الخروج
      </button>
    </div>
  </header>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">جاري التحميل...</span>
    </div>
    <p>جاري تحميل البيانات...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="alert alert-danger">
    {{ error }}
    <button class="btn btn-sm btn-outline-danger ms-2" (click)="loadDashboardData()">
      إعادة المحاولة
    </button>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!loading && !error" class="dashboard-content">
    
    <!-- Admin Dashboard -->
    <div *ngIf="currentUser?.role === 'Admin'" class="admin-dashboard">
      <div class="row">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h3>إدارة التكوينات</h3>
            </div>
            <div class="card-body">
              <p>عدد التكوينات: {{ formations.length }}</p>
              <div class="action-buttons">
                <button class="btn btn-primary" routerLink="/formations">
                  عرض التكوينات
                </button>
                <button class="btn btn-success" routerLink="/formations/create">
                  إضافة تكوين جديد
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h3>إدارة الاستمارات</h3>
            </div>
            <div class="card-body">
              <p>عدد الاستمارات: {{ formulaires.length }}</p>
              <div class="action-buttons">
                <button class="btn btn-primary" routerLink="/formulaires">
                  عرض الاستمارات
                </button>
                <button class="btn btn-success" routerLink="/formulaires/create">
                  إنشاء استمارة جديدة
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h3>إدارة المستخدمين</h3>
            </div>
            <div class="card-body">
              <div class="action-buttons">
                <button class="btn btn-primary" routerLink="/users">
                  عرض المستخدمين
                </button>
                <button class="btn btn-success" routerLink="/users/add-student">
                  إضافة طالب
                </button>
                <button class="btn btn-success" routerLink="/users/add-professor">
                  إضافة أستاذ
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Student Dashboard -->
    <div *ngIf="currentUser?.role === 'Etudiant'" class="student-dashboard">
      <div class="row">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h3>الاستمارات المتاحة</h3>
            </div>
            <div class="card-body">
              <div *ngIf="formulaires.length === 0" class="text-center">
                <p>لا توجد استمارات متاحة حالياً</p>
              </div>
              <div *ngFor="let formulaire of formulaires" class="formulaire-item">
                <h5>{{ formulaire.name }}</h5>
                <p>النوع: {{ formulaire.type }}</p>
                <button class="btn btn-primary" [routerLink]="['/formulaires', formulaire.formulaireId, 'fill']">
                  ملء الاستمارة
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h3>معلومات التكوين</h3>
            </div>
            <div class="card-body">
              <div *ngIf="formations.length > 0">
                <h5>{{ formations[0].formationName }}</h5>
                <p><strong>المؤسسة:</strong> {{ formations[0].schoolName }}</p>
                <p><strong>الوصف:</strong> {{ formations[0].description }}</p>
              </div>
              <div *ngIf="formations.length === 0">
                <p>لم يتم تحديد تكوين لهذا المستخدم</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Professor Dashboard -->
    <div *ngIf="currentUser?.role === 'Enseignant' || currentUser?.role === 'Professionnel'" class="professor-dashboard">
      <div class="row">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h3>الاستمارات المتاحة للتقييم</h3>
            </div>
            <div class="card-body">
              <div *ngIf="formulaires.length === 0" class="text-center">
                <p>لا توجد استمارات متاحة حالياً</p>
              </div>
              <div *ngFor="let formulaire of formulaires" class="formulaire-item">
                <h5>{{ formulaire.name }}</h5>
                <p>النوع: {{ formulaire.type }}</p>
                <div class="action-buttons">
                  <button class="btn btn-primary" [routerLink]="['/formulaires', formulaire.formulaireId, 'fill']">
                    ملء الاستمارة
                  </button>
                  <button class="btn btn-info" [routerLink]="['/formulaires', formulaire.formulaireId, 'statistics']">
                    عرض الإحصائيات
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h3>معلومات التكوين</h3>
            </div>
            <div class="card-body">
              <div *ngIf="formations.length > 0">
                <h5>{{ formations[0].formationName }}</h5>
                <p><strong>المؤسسة:</strong> {{ formations[0].schoolName }}</p>
                <p><strong>الوصف:</strong> {{ formations[0].description }}</p>
              </div>
              <div *ngIf="formations.length === 0">
                <p>لم يتم تحديد تكوين لهذا المستخدم</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
