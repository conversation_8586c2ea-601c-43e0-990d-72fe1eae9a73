.api-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
}

.header p {
  margin: 0;
  font-size: 1.2em;
  opacity: 0.9;
}

.environment-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
  text-align: left;
}

.environment-info h3 {
  margin: 0 0 10px 0;
}

.environment-info ul {
  margin: 0;
  padding-left: 20px;
}

.environment-info li {
  margin: 5px 0;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.test-results h2 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.test-card {
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 20px;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.test-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.test-header h3 {
  margin: 0;
  font-size: 1.3em;
}

.retry-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 0.8em;
  transition: background-color 0.3s ease;
}

.retry-btn:hover:not(:disabled) {
  background: #e9ecef;
}

.retry-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.status-message {
  font-weight: 500;
  margin-bottom: 10px;
}

.test-data {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 5px;
  border-left: 4px solid #007bff;
}

.test-data h4 {
  margin: 0 0 10px 0;
  color: #495057;
}

.data-item {
  padding: 5px 0;
  border-bottom: 1px solid #e9ecef;
}

.data-item:last-child {
  border-bottom: none;
}

.test-data pre {
  background: #343a40;
  color: #f8f9fa;
  padding: 10px;
  border-radius: 5px;
  overflow-x: auto;
  font-size: 0.8em;
}

/* Status-specific styles */
.status-success {
  border-color: #28a745;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.status-success .status-message {
  color: #155724;
}

.status-error {
  border-color: #dc3545;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.status-error .status-message {
  color: #721c24;
}

.status-pending {
  border-color: #ffc107;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.status-pending .status-message {
  color: #856404;
}

.actions {
  text-align: center;
  margin: 30px 0;
}

.test-all-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.test-all-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(102, 126, 234, 0.4);
}

.test-all-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.instructions {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 5px solid #007bff;
}

.instructions h3 {
  margin: 0 0 15px 0;
  color: #495057;
}

.instructions ol {
  margin: 0;
  padding-left: 20px;
}

.instructions li {
  margin: 10px 0;
  line-height: 1.6;
}

.instructions ul {
  margin: 10px 0;
  padding-left: 20px;
}

.instructions ul li {
  margin: 5px 0;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  background: #e9ecef;
  padding: 2px 5px;
  border-radius: 3px;
}
