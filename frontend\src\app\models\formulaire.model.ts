export interface Formulaire {
  formulaireId: number;
  name: string;
  type: string;
  sections: SectionFormulaire[];
}

export interface SectionFormulaire {
  secFormId: number;
  description: string;
  questions: Question[];
}

export interface Question {
  questionId: number;
  content: string;
}

export interface FormulaireDTO {
  name: string;
  type: string;
  sections: SectionFormulaireDTO[];
}

export interface SectionFormulaireDTO {
  description: string;
  questions: QuestionDTO[];
}

export interface QuestionDTO {
  content: string;
}

export interface FormulaireReponseDTO {
  formationId: number;
  niveauId: number;
  moduleId: number;
  userId: number;
  reponses: ReponseDTO[];
}

export interface ReponseDTO {
  questionId: number;
  score: number;
}

export interface FormulaireProfDTO {
  formationId: number;
  niveauId: number;
  moduleId: number;
  userId: number;
  reponses: ReponseProfDTO[];
}

export interface ReponseProfDTO {
  questionId: number;
  score: number;
}

export interface StatistiqueScoreFormationDTO {
  formationId: number;
  niveauId: number;
  modules: ModuleStatistique[];
}

export interface ModuleStatistique {
  moduleId: number;
  moduleName: string;
  averageScore: number;
  totalResponses: number;
}
