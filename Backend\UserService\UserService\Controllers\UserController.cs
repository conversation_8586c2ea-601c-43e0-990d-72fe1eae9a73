using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UserService.Models.DTO;
using UserService.Services;

namespace UserService.Controllers
{
    [Route("api/users")]
    [ApiController]
    public class UserController : Controller
    {
        private readonly UserDbContext _context;


        public UserController(UserDbContext context)
        {
            _context = context;
        }

        [HttpPost("change-password")]
        public IActionResult ChangePassword(ChangePasswordDto dto)
        {
            var user = _context.Users.Find(dto.UserId);
            if (user == null)
                return NotFound();

            user.Password = PasswordHasher.HashPassword(dto.NewPassword);
            user.IsFirstLogin = false;
            _context.SaveChanges();

            return Ok(new { message = "Mot de passe modifié avec succès" });
        }


        [HttpPost("update-niveau")]
        public IActionResult UpdateStudentNiveau(UpdateStudentNiveauDto dto)
        {
            var student = _context.Students.FirstOrDefault(s => s.UserId == dto.UserId);
            if (student == null)
                return NotFound(new { message = "Étudiant introuvable." });

            student.NiveauId = dto.NiveauId;
            _context.SaveChanges();

            return Ok(new { message = "Niveau de l'étudiant mis à jour avec succès." });
        }

        [HttpGet("health")]
        public IActionResult HealthCheck()
        {
            return Ok(new {
                status = "healthy",
                service = "UserService",
                timestamp = DateTime.UtcNow,
                version = "1.0.0"
            });
        }

        [HttpGet]
        public IActionResult GetAllUsers()
        {
            var users = _context.Users.Select(u => new
            {
                id = u.Id,
                firstName = u.firstName,
                lastName = u.lastName,
                email = u.Email,
                role = u.Role,
                formationId = u.FormationId,
                isFirstLogin = u.IsFirstLogin
            }).ToList();

            return Ok(users);
        }

        [HttpGet("{id}")]
        public IActionResult GetUserById(int id)
        {
            var user = _context.Users.Find(id);
            if (user == null)
                return NotFound(new { message = "Utilisateur introuvable." });

            return Ok(new
            {
                id = user.Id,
                firstName = user.firstName,
                lastName = user.lastName,
                email = user.Email,
                role = user.Role,
                formationId = user.FormationId,
                isFirstLogin = user.IsFirstLogin
            });
        }

        [HttpPut("{id}")]
        public IActionResult UpdateUser(int id, [FromBody] dynamic userData)
        {
            var user = _context.Users.Find(id);
            if (user == null)
                return NotFound(new { message = "Utilisateur introuvable." });

            // Update user properties if provided
            if (userData.firstName != null)
                user.firstName = userData.firstName;
            if (userData.lastName != null)
                user.lastName = userData.lastName;
            if (userData.email != null)
                user.Email = userData.email;
            if (userData.formationId != null)
                user.FormationId = userData.formationId;

            _context.SaveChanges();

            return Ok(new
            {
                id = user.Id,
                firstName = user.firstName,
                lastName = user.lastName,
                email = user.Email,
                role = user.Role,
                formationId = user.FormationId,
                isFirstLogin = user.IsFirstLogin
            });
        }

        [HttpDelete("{id}")]
        public IActionResult DeleteUser(int id)
        {
            var user = _context.Users.Find(id);
            if (user == null)
                return NotFound(new { message = "Utilisateur introuvable." });

            // Delete related records first
            var student = _context.Students.FirstOrDefault(s => s.UserId == id);
            if (student != null)
                _context.Students.Remove(student);

            var professor = _context.Profs.FirstOrDefault(p => p.UserId == id);
            if (professor != null)
                _context.Profs.Remove(professor);

            var admin = _context.Admins.FirstOrDefault(a => a.UserId == id);
            if (admin != null)
                _context.Admins.Remove(admin);

            _context.Users.Remove(user);
            _context.SaveChanges();

            return Ok(new { message = "Utilisateur supprimé avec succès." });
        }

    }
}
