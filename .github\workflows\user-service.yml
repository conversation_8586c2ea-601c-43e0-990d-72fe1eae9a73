name: User Service CI/CD

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop

env:
  DOTNET_VERSION: '9.0.x'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: dev
    steps:
      - name: 'Checkout repository'
        uses: actions/checkout@v3

      - name: 'Setup .NET ${{ env.DOTNET_VERSION }}'
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: 'Restore dependencies'
        run: |
          cd Backend/UserService/UserService
          dotnet restore

      - name: 'Build project'
        run: |
          cd Backend/UserService/UserService
          dotnet build --no-restore

      - name: 'Login to Docker Hub'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: 'Build Docker image'
        uses: docker/build-push-action@v5
        with:
          context: ./Backend/UserService/UserService
          file: ./Backend/UserService/UserService/Dockerfile
          push: false
          tags: ${{ secrets.DOCKER_USERNAME }}/user-service:latest

      - name: 'Push Docker image to Docker Hub'
        uses: docker/build-push-action@v5
        with:
          context: ./Backend/UserService/UserService
          file: ./Backend/UserService/UserService/Dockerfile
          push: true
          tags: ${{ secrets.DOCKER_USERNAME }}/user-service:latest
