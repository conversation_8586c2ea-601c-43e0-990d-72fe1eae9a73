# PowerShell script to install all required dependencies for RateMyCourse
# Run as Administrator

Write-Host "🚀 Installing RateMyCourse Requirements..." -ForegroundColor Green
Write-Host "⚠️  Please run this script as Administrator" -ForegroundColor Yellow
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script requires Administrator privileges!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Function to check if a command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Install Chocolatey if not present
if (-not (Test-Command "choco")) {
    Write-Host "📦 Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
}

Write-Host "✅ Chocolatey is ready" -ForegroundColor Green

# Install Node.js
if (-not (Test-Command "node")) {
    Write-Host "📦 Installing Node.js..." -ForegroundColor Yellow
    choco install nodejs -y
} else {
    Write-Host "✅ Node.js is already installed" -ForegroundColor Green
}

# Install .NET 6 SDK
if (-not (Test-Command "dotnet")) {
    Write-Host "📦 Installing .NET 6 SDK..." -ForegroundColor Yellow
    choco install dotnet-6.0-sdk -y
} else {
    Write-Host "✅ .NET SDK is already installed" -ForegroundColor Green
}

# Install Java
if (-not (Test-Command "java")) {
    Write-Host "📦 Installing Java 11..." -ForegroundColor Yellow
    choco install openjdk11 -y
} else {
    Write-Host "✅ Java is already installed" -ForegroundColor Green
}

# Install Maven
if (-not (Test-Command "mvn")) {
    Write-Host "📦 Installing Maven..." -ForegroundColor Yellow
    choco install maven -y
} else {
    Write-Host "✅ Maven is already installed" -ForegroundColor Green
}

# Install PostgreSQL
Write-Host "📦 Installing PostgreSQL..." -ForegroundColor Yellow
choco install postgresql -y

# Refresh environment variables
Write-Host "🔄 Refreshing environment variables..." -ForegroundColor Yellow
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

Write-Host ""
Write-Host "✅ Installation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart your PowerShell/Command Prompt" -ForegroundColor White
Write-Host "2. Setup PostgreSQL databases (see SETUP-GUIDE-AR.md)" -ForegroundColor White
Write-Host "3. Run: .\start-backend.ps1" -ForegroundColor White
Write-Host "4. Run: .\start-frontend.ps1" -ForegroundColor White
Write-Host ""
Write-Host "📖 For detailed setup instructions, see: SETUP-GUIDE-AR.md" -ForegroundColor Yellow

# Check versions
Write-Host ""
Write-Host "🔍 Checking installed versions..." -ForegroundColor Cyan

try {
    $nodeVersion = node --version
    Write-Host "Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Node.js: Not found (restart required)" -ForegroundColor Red
}

try {
    $dotnetVersion = dotnet --version
    Write-Host ".NET SDK: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host ".NET SDK: Not found (restart required)" -ForegroundColor Red
}

try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "Java: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "Java: Not found (restart required)" -ForegroundColor Red
}

try {
    $mvnVersion = mvn --version | Select-String "Apache Maven"
    Write-Host "Maven: $mvnVersion" -ForegroundColor Green
} catch {
    Write-Host "Maven: Not found (restart required)" -ForegroundColor Red
}

Write-Host ""
Write-Host "⚠️  If any tools show 'Not found', please restart your terminal and try again." -ForegroundColor Yellow
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
