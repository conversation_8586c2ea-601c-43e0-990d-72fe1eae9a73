import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { FormationService } from '../../services/formation.service';
import { FormulaireService } from '../../services/formulaire.service';
import { User } from '../../models/user.model';
import { FormationResponseDTO } from '../../models/formation.model';
import { Formulaire } from '../../models/formulaire.model';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;
  formations: FormationResponseDTO[] = [];
  formulaires: Formulaire[] = [];
  loading = true;
  error = '';

  constructor(
    private authService: AuthService,
    private formationService: FormationService,
    private formulaireService: FormulaireService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.loading = true;
    this.error = '';

    // تحميل البيانات حسب دور المستخدم
    if (this.currentUser) {
      if (this.currentUser.role === 'Admin') {
        this.loadAdminData();
      } else if (this.currentUser.role === 'Etudiant') {
        this.loadStudentData();
      } else if (this.currentUser.role === 'Enseignant' || this.currentUser.role === 'Professionnel') {
        this.loadProfessorData();
      }
    }
  }

  loadAdminData(): void {
    // تحميل جميع التكوينات والاستمارات للمدير
    Promise.all([
      this.formationService.getAllFormations().toPromise(),
      this.formulaireService.getAllFormulaires().toPromise()
    ]).then(([formations, formulaires]) => {
      this.formations = formations || [];
      this.formulaires = formulaires || [];
      this.loading = false;
    }).catch(error => {
      this.error = 'حدث خطأ في تحميل البيانات';
      this.loading = false;
      console.error('Error loading admin data:', error);
    });
  }

  loadStudentData(): void {
    // تحميل البيانات الخاصة بالطالب
    if (this.currentUser?.formationId) {
      this.formationService.getFormationById(this.currentUser.formationId)
        .subscribe({
          next: (formation) => {
            this.formations = [formation];
            this.loadFormulaires();
          },
          error: (error) => {
            this.error = 'حدث خطأ في تحميل بيانات التكوين';
            this.loading = false;
            console.error('Error loading student formation:', error);
          }
        });
    } else {
      this.loadFormulaires();
    }
  }

  loadProfessorData(): void {
    // تحميل البيانات الخاصة بالأستاذ
    if (this.currentUser?.formationId) {
      this.formationService.getFormationById(this.currentUser.formationId)
        .subscribe({
          next: (formation) => {
            this.formations = [formation];
            this.loadFormulaires();
          },
          error: (error) => {
            this.error = 'حدث خطأ في تحميل بيانات التكوين';
            this.loading = false;
            console.error('Error loading professor formation:', error);
          }
        });
    } else {
      this.loadFormulaires();
    }
  }

  loadFormulaires(): void {
    this.formulaireService.getAllFormulaires()
      .subscribe({
        next: (formulaires) => {
          this.formulaires = formulaires;
          this.loading = false;
        },
        error: (error) => {
          this.error = 'حدث خطأ في تحميل الاستمارات';
          this.loading = false;
          console.error('Error loading formulaires:', error);
        }
      });
  }

  logout(): void {
    this.authService.logout();
  }

  getUserRoleDisplay(): string {
    if (!this.currentUser) return '';
    
    switch (this.currentUser.role) {
      case 'Admin': return 'مدير النظام';
      case 'Etudiant': return 'طالب';
      case 'Enseignant': return 'أستاذ';
      case 'Professionnel': return 'مهني';
      default: return this.currentUser.role;
    }
  }
}
