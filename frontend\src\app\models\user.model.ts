export interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  formationId: number;
  isFirstLogin: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token?: string;
  user?: User;
  success?: boolean;
  message: string;
  firstLogin?: boolean;
  userId?: number;
  role?: string;
}

export interface AddUserDto {
  firstName: string;
  lastName: string;
  email: string;
  formationId: number;
  niveauId?: number;
}

export interface ChangePasswordDto {
  userId: number;
  newPassword: string;
}

export interface Student {
  userId: number;
  niveauId: number;
}

export interface Professor {
  userId: number;
}

export interface Admin {
  userId: number;
}
