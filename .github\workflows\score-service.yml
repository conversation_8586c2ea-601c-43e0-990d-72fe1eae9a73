name: Build and Push Docker Images

on:
  push:
    branches:
      - develop

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '21' 

      - name: Build backend JAR
        run: |
          cd Backend/ScoreService
          mvn clean package -DskipTests

      - name: Log in to Docker Hub
        run: |
          echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin

      - name: Build Score service Image
        run: |
          cd Backend/ScoreService
          docker build -t ${{ secrets.DOCKER_USERNAME }}/score-service:latest .

      - name: Push Score service Image
        run: |
          docker push ${{ secrets.DOCKER_USERNAME }}/score-service:latest
