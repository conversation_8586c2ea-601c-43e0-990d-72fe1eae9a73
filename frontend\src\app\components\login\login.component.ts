import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  loading = false;
  error = '';
  returnUrl = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    // إذا كان المستخدم مسجل دخول بالفعل، توجيهه للصفحة الرئيسية
    if (this.authService.isLoggedIn()) {
      this.router.navigate(['/dashboard']);
      return;
    }

    // الحصول على URL العودة
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
  }

  get f() { return this.loginForm.controls; }

  onSubmit(): void {
    if (this.loginForm.invalid) {
      return;
    }

    this.loading = true;
    this.error = '';

    const credentials = {
      email: this.f['email'].value,
      password: this.f['password'].value
    };

    this.authService.login(credentials).subscribe({
      next: (response) => {
        this.loading = false;
        if (response.firstLogin) {
          // Handle first login - redirect to change password
          this.router.navigate(['/change-password'], {
            queryParams: {
              userId: response.userId,
              role: response.role
            }
          });
        } else if (response.success) {
          this.router.navigate([this.returnUrl]);
        } else {
          this.error = response.message || 'فشل في تسجيل الدخول';
        }
      },
      error: (error) => {
        this.loading = false;
        this.error = error.message || 'حدث خطأ أثناء تسجيل الدخول';
      }
    });
  }
}
