<div class="change-password-container">
  <div class="change-password-card">
    <div class="card-header">
      <h2>تغيير كلمة المرور</h2>
      <p>يجب تغيير كلمة المرور في أول تسجيل دخول</p>
    </div>

    <form [formGroup]="changePasswordForm" (ngSubmit)="onSubmit()" class="change-password-form">
      <div class="form-group">
        <label for="newPassword">كلمة المرور الجديدة</label>
        <input
          type="password"
          id="newPassword"
          formControlName="newPassword"
          class="form-control"
          [class.is-invalid]="f['newPassword'].invalid && f['newPassword'].touched"
          placeholder="أدخل كلمة المرور الجديدة"
        />
        <div *ngIf="f['newPassword'].invalid && f['newPassword'].touched" class="invalid-feedback">
          <div *ngIf="f['newPassword'].errors?.['required']">كلمة المرور مطلوبة</div>
          <div *ngIf="f['newPassword'].errors?.['minlength']">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
        </div>
      </div>

      <div class="form-group">
        <label for="confirmPassword">تأكيد كلمة المرور</label>
        <input
          type="password"
          id="confirmPassword"
          formControlName="confirmPassword"
          class="form-control"
          [class.is-invalid]="(f['confirmPassword'].invalid && f['confirmPassword'].touched) || changePasswordForm.errors?.['passwordMismatch']"
          placeholder="أكد كلمة المرور الجديدة"
        />
        <div *ngIf="f['confirmPassword'].invalid && f['confirmPassword'].touched" class="invalid-feedback">
          <div *ngIf="f['confirmPassword'].errors?.['required']">تأكيد كلمة المرور مطلوب</div>
        </div>
        <div *ngIf="changePasswordForm.errors?.['passwordMismatch'] && f['confirmPassword'].touched" class="invalid-feedback">
          كلمات المرور غير متطابقة
        </div>
      </div>

      <div *ngIf="error" class="alert alert-danger">
        {{ error }}
      </div>

      <div *ngIf="success" class="alert alert-success">
        {{ success }}
      </div>

      <button
        type="submit"
        class="btn btn-primary btn-block"
        [disabled]="changePasswordForm.invalid || loading"
      >
        <span *ngIf="loading" class="spinner-border spinner-border-sm" role="status"></span>
        {{ loading ? 'جاري التحديث...' : 'تغيير كلمة المرور' }}
      </button>
    </form>
  </div>
</div>
