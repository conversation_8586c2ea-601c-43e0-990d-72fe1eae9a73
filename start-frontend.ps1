# PowerShell script to start the Angular frontend
# Run this script from the project root directory

Write-Host "🚀 Starting RateMyCourse Frontend..." -ForegroundColor Green

# Check if we're in the correct directory
if (-not (Test-Path "frontend")) {
    Write-Host "❌ Error: Please run this script from the project root directory (where frontend folder exists)" -ForegroundColor Red
    exit 1
}

# Navigate to frontend directory
Set-Location "frontend"

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    npm install
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Error: Failed to install dependencies" -ForegroundColor Red
        exit 1
    }
}

Write-Host "🌐 Starting Angular development server..." -ForegroundColor Cyan
Write-Host "📍 Frontend will be available at: http://localhost:4200" -ForegroundColor Green
Write-Host "🧪 API Test page will be available at: http://localhost:4200/api-test" -ForegroundColor Green
Write-Host ""

# Start the Angular development server
npm start
