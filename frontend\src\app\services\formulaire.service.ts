import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import {
  Formulaire,
  FormulaireDTO,
  FormulaireReponseDTO,
  FormulaireProfDTO,
  StatistiqueScoreFormationDTO
} from '../models/formulaire.model';

@Injectable({
  providedIn: 'root'
})
export class FormulaireService extends BaseApiService {

  constructor(http: HttpClient) {
    super(http);
  }

  getAllFormulaires(): Observable<Formulaire[]> {
    return this.get<Formulaire[]>('/Formulaires');
  }

  getFormulaireById(id: number): Observable<Formulaire> {
    return this.get<Formulaire>(`/Formulaires/${id}`);
  }

  createFormulaire(formulaire: FormulaireDTO): Observable<Formulaire> {
    return this.post<Formulaire>('/Formulaires', formulaire);
  }

  updateFormulaire(id: number, formulaire: FormulaireDTO): Observable<Formulaire> {
    return this.put<Formulaire>(`/Formulaires/${id}`, formulaire);
  }

  deleteFormulaire(id: number): Observable<any> {
    return this.delete(`/Formulaires/${id}`);
  }

  getFormulaireByType(type: string): Observable<Formulaire[]> {
    return this.get<Formulaire[]>(`/Formulaires/type/${type}`);
  }

  // خدمات الردود والإحصائيات
  submitStudentResponse(response: FormulaireReponseDTO): Observable<any> {
    return this.post('/responses/etudiant', response);
  }

  submitProfessorResponse(response: FormulaireProfDTO): Observable<any> {
    return this.post('/responses/professeur', response);
  }

  getStatistiquesModules(formationId: number, niveauId: number): Observable<StatistiqueScoreFormationDTO> {
    return this.get<StatistiqueScoreFormationDTO>(`/responses/statistiques/modules?formationId=${formationId}&niveauId=${niveauId}`);
  }

  getStatistiquesSections(formationId: number, niveauId: number, moduleId: number): Observable<any> {
    return this.get<any>(`/responses/statistiques/sections?formationId=${formationId}&niveauId=${niveauId}&moduleId=${moduleId}`);
  }
}
